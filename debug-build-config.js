// Quick debug script to test B<PERSON>LD_CONFIG in browser console
// Run this in browser console when on localhost:3000

console.log('🔍 BUILD_CONFIG Debug:');
console.log('window.location.hostname:', window.location.hostname);
console.log('window.location.port:', window.location.port);
console.log('process.env.BUILD_TARGET:', process.env.BUILD_TARGET);
console.log('process.env.NODE_ENV:', process.env.NODE_ENV);

// Simulate the BUILD_CONFIG logic
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
const remoteServerUrl = isLocalhost ? 'http://localhost:3000' : 'https://bistro.icu';

console.log('isLocalhost:', isLocalhost);
console.log('remoteServerUrl should be:', remoteServerUrl);

// Test the actual BUILD_CONFIG if available
if (typeof BUILD_CONFIG !== 'undefined') {
  console.log('BUILD_CONFIG.remoteServerUrl:', BUILD_CONFIG.remoteServerUrl);
  console.log('BUILD_CONFIG.isDevelopment:', BUILD_CONFIG.isDevelopment);
  console.log('BUILD_CONFIG.features.apiRoutes:', BUILD_CONFIG.features.apiRoutes);
}

// Test API URL construction
const testEndpoint = 'auth/login';
const expectedUrl = `${remoteServerUrl}/api/${testEndpoint}`;
console.log('Expected API URL:', expectedUrl);

// Test connectivity to localhost
fetch('http://localhost:3000/api/health')
  .then(response => {
    console.log('✅ localhost:3000/api/health reachable:', response.status);
    return response.json();
  })
  .then(data => console.log('Health response:', data))
  .catch(error => console.log('❌ localhost:3000/api/health failed:', error));