// Clean up test orders from the database
// Run this in the browser console on the ordering page

async function cleanTestOrders() {
  console.log('🧹 Starting test order cleanup...');
  
  try {
    // Import the database operations
    const { getAllOrders, deleteOrder } = await import('./lib/db/v4/operations/order-ops.ts');
    
    // Get all orders
    const orders = await getAllOrders();
    console.log(`Found ${orders.length} orders`);
    
    // Find test orders (orders with test data)
    const testOrders = orders.filter(order => {
      const hasTestCustomer = order.customer?.name?.includes('Test') || 
                             order.customer?.name?.includes('Co') ||
                             order.customer?.address?.includes('Collection Street');
      
      const hasTestItems = order.items?.some(item => 
        item.name?.includes('Test') || 
        item.name?.includes('Burger')
      );
      
      return hasTestCustomer || hasTestItems;
    });
    
    console.log(`Found ${testOrders.length} test orders to delete`);
    
    // Delete test orders
    for (const order of testOrders) {
      try {
        await deleteOrder(order.id);
        console.log(`✅ Deleted test order: ${order.id}`);
      } catch (error) {
        console.error(`❌ Failed to delete order ${order.id}:`, error);
      }
    }
    
    console.log('✅ Test order cleanup completed!');
    
    // Clear localStorage as well
    localStorage.removeItem('current_edit_session');
    localStorage.removeItem('devTestMode');
    
    // Clear any UI state
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('ui_state_')) {
        localStorage.removeItem(key);
      }
    });
    
    console.log('✅ LocalStorage cleaned! Please refresh the page.');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

// Run the cleanup
cleanTestOrders();