"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, Plus, X, ChefHat, Package } from "lucide-react";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { SubRecipeForm } from "./SubRecipeForm";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

const menuItemRecipeSchema = z.object({
  menuItemId: z.string().min(1, { message: "Veuillez sélectionner un article du menu." }),
  size: z.string().optional(),
  ingredients: z.array(
    z.object({
      type: z.enum(["stock", "subRecipe"]),
      stockItemId: z.string().optional(),
      subRecipeId: z.string().optional(),
      quantity: z.coerce.number().positive({ message: "La quantité doit être positive." }),
    }).refine(data => {
      if (data.type === "stock") {
        return !!data.stockItemId;
      } else {
        return !!data.subRecipeId;
      }
    }, {
      message: "Veuillez sélectionner un ingrédient ou une sous-recette.",
      path: ["stockItemId"]
    })
  ).min(1, { message: "Au moins un ingrédient est requis." }),
});

interface MenuItem {
  id: string;
  name: string;
  categoryName: string;
  sizes: string[];
  prices: Record<string, number>;
}

interface MenuItemRecipeFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  initialData?: any | null;
  menuItems: MenuItem[];
  subRecipes: any[];
  existingRecipes?: any[];
  selectedSize?: string;
  isMobile?: boolean;
}

export function MenuItemRecipeForm({ 
  onSubmit, 
  onCancel, 
  initialData, 
  menuItems, 
  subRecipes, 
  existingRecipes = [],
  selectedSize = "",
  isMobile = false 
}: MenuItemRecipeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { stockItems } = useStockV4();
  const { createSubRecipe } = useCOGSV4();
  const { toast } = useToast();
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [isSubRecipeDialogOpen, setIsSubRecipeDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof menuItemRecipeSchema>>({
    resolver: zodResolver(menuItemRecipeSchema),
    defaultValues: initialData
      ? {
          menuItemId: initialData.menuItemId,
          size: initialData.size || "",
          ingredients: initialData.ingredients.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }),
        }
      : {
          menuItemId: menuItems[0]?.id || "",
          size: selectedSize,
          ingredients: [{ type: "stock", stockItemId: "", quantity: 1 }],
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "ingredients",
  });

  useEffect(() => {
    const menuItemId = form.watch("menuItemId");
    if (menuItemId) {
      const menuItem = menuItems.find(item => item.id === menuItemId);
      setSelectedMenuItem(menuItem || null);
    } else {
      setSelectedMenuItem(null);
    }
  }, [form.watch("menuItemId"), menuItems]);

  // Initialize form with selected size and load correct recipe
  useEffect(() => {
    if (menuItems[0] && selectedSize) {
      const menuItemId = menuItems[0].id;
      const existingRecipe = existingRecipes.find(r => 
        r.menuItemId === menuItemId && r.size === selectedSize
      );
      
      if (existingRecipe) {
        // Load existing recipe data for this size
        form.reset({
          menuItemId: existingRecipe.menuItemId,
          size: existingRecipe.size || "",
          ingredients: existingRecipe.ingredients.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }),
        });
      } else {
        // Reset to empty recipe for this size
        form.reset({
          menuItemId,
          size: selectedSize,
          ingredients: [{ type: "stock", stockItemId: "", quantity: 1 }],
        });
      }
    }
  }, [selectedSize, existingRecipes, menuItems, form]);

  // Handle size changes to load the correct recipe
  useEffect(() => {
    const menuItemId = form.watch("menuItemId");
    const size = form.watch("size");
    
    if (menuItemId && size && existingRecipes.length > 0) {
      const existingRecipe = existingRecipes.find(r => 
        r.menuItemId === menuItemId && r.size === size
      );
      
      if (existingRecipe) {
        // Load existing recipe data for this size
        form.reset({
          menuItemId: existingRecipe.menuItemId,
          size: existingRecipe.size || "",
          ingredients: existingRecipe.ingredients.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }),
        });
      } else if (menuItemId && size) {
        // Reset to empty recipe for this size
        form.reset({
          menuItemId,
          size,
          ingredients: [{ type: "stock", stockItemId: "", quantity: 1 }],
        });
      }
    }
  }, [form.watch("menuItemId"), form.watch("size"), existingRecipes, form]);

  const handleSubmit = async (values: z.infer<typeof menuItemRecipeSchema>) => {
    setIsSubmitting(true);
    try {
      const transformedIngredients = values.ingredients.map(ingredient => {
        if (ingredient.type === "stock") {
          return {
            stockItemId: ingredient.stockItemId!,
            quantity: ingredient.quantity,
          };
        } else {
          return {
            subRecipeId: ingredient.subRecipeId!,
            quantity: ingredient.quantity,
          };
        }
      });

      await onSubmit({
        ...values,
        ingredients: transformedIngredients,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubRecipeSubmit = async (data: any) => {
    try {
      await createSubRecipe(data);
      toast({
        title: "✅ Sous-recette créée",
        description: `${data.name} a été créée avec succès.`,
        duration: 2000,
      });
      setIsSubRecipeDialogOpen(false);
      // The parent component will refresh the subRecipes list automatically
    } catch (error) {
      toast({
        title: "❌ Erreur",
        description: "Impossible de créer la sous-recette.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  return (
    <div className="space-y-3">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3">
          {/* Menu Item Selection - Ultra Compact */}
          <div className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="menuItemId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs font-medium">Article</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Sélectionner un article" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {menuItems.map((item) => (
                          <SelectItem key={item.id} value={item.id} className="text-xs">
                            {item.name} ({item.categoryName})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedMenuItem && selectedMenuItem.sizes && selectedMenuItem.sizes.length > 0 && (
                <FormField
                  control={form.control}
                  name="size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs font-medium">Taille</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue placeholder="Taille" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {selectedMenuItem.sizes.map((size) => (
                            <SelectItem key={size} value={size} className="text-xs">
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
          </div>

          {/* Recipe Ingredients - Ultra Compact */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <FormLabel className="text-xs font-medium">Ingrédients</FormLabel>
              <div className="flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSubRecipeDialogOpen(true)}
                  className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                >
                  <ChefHat className="h-3 w-3 mr-1" />
                  Nouvelle sous-recette
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => append({ type: "stock", stockItemId: "", quantity: 1 })}
                  className="h-6 px-2 text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Ajouter
                </Button>
              </div>
            </div>

            <div className="space-y-1 max-h-[350px] overflow-y-auto">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-2 p-2 border rounded-md bg-muted/20">
                  {/* Type Selection - Compact Radio */}
                  <FormField
                    control={form.control}
                    name={`ingredients.${index}.type`}
                    render={({ field }) => (
                      <FormItem className="space-y-0">
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex gap-2"
                          >
                            <div className="flex items-center space-x-1">
                              <RadioGroupItem value="stock" id={`stock-${index}`} className="h-3 w-3" />
                              <Label htmlFor={`stock-${index}`} className="text-xs flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                Stock
                              </Label>
                            </div>
                            <div className="flex items-center space-x-1">
                              <RadioGroupItem value="subRecipe" id={`subrecipe-${index}`} className="h-3 w-3" />
                              <Label htmlFor={`subrecipe-${index}`} className="text-xs flex items-center gap-1">
                                <ChefHat className="h-3 w-3" />
                                Sous-recette
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Ingredient Selection */}
                  <div className="flex-1">
                    {form.watch(`ingredients.${index}.type`) === "stock" ? (
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.stockItemId`}
                        render={({ field }) => (
                          <FormItem>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-7 text-xs">
                                  <SelectValue placeholder="Sélectionner un ingrédient" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {stockItems.map((item) => (
                                  <SelectItem key={item.id} value={item.id} className="text-xs">
                                    <div className="flex items-center gap-2">
                                      <Package className="h-3 w-3" />
                                      {item.name}
                                      <Badge variant="outline" className="text-xs">{item.unit}</Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.subRecipeId`}
                        render={({ field }) => (
                          <FormItem>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-7 text-xs">
                                  <SelectValue placeholder="Sélectionner une sous-recette" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {subRecipes.map((subRecipe, idx) => (
                                  <SelectItem key={subRecipe._id + '-' + idx} value={subRecipe._id} className="text-xs">
                                    <div className="flex items-center gap-2">
                                      <ChefHat className="h-3 w-3" />
                                      {subRecipe.name}
                                      <Badge variant="outline" className="text-xs">{subRecipe.yield.unit}</Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  {/* Quantity Input */}
                  <div className="w-16">
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              min="0.01"
                              step="0.01"
                              placeholder="1"
                              className="h-7 text-xs text-center"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Remove Button */}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-muted-foreground hover:text-destructive"
                    onClick={() => remove(index)}
                    disabled={fields.length === 1}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>

            {form.formState.errors.ingredients?.message && (
              <p className="text-xs font-medium text-destructive">
                {form.formState.errors.ingredients.message}
              </p>
            )}
          </div>

          {/* Actions - Compact */}
          <div className="flex gap-2 pt-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1 h-8 text-xs"
            >
              Annuler
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="flex-1 h-8 text-xs"
            >
              {isSubmitting && <Loader2 className="mr-1 h-3 w-3 animate-spin" />}
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>

      {/* Sub-Recipe Creation Dialog */}
      <Dialog open={isSubRecipeDialogOpen} onOpenChange={setIsSubRecipeDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5 text-blue-600" />
              Créer une nouvelle sous-recette
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            <SubRecipeForm
              onSubmit={handleSubRecipeSubmit}
              onCancel={() => setIsSubRecipeDialogOpen(false)}
              stockItems={stockItems}
              isMobile={isMobile}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
