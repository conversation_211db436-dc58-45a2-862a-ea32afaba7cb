// Clear all test data from localStorage and reset ordering interface
console.log('🧹 Clearing all test data...');

// Clear any edit sessions
localStorage.removeItem('current_edit_session');

// Clear any dev test mode
localStorage.removeItem('devTestMode');

// Clear any UI state data
const keys = Object.keys(localStorage);
keys.forEach(key => {
  if (key.startsWith('ui_state_')) {
    localStorage.removeItem(key);
    console.log(`Removed: ${key}`);
  }
});

// Clear any cached order data
keys.forEach(key => {
  if (key.includes('order') || key.includes('test') || key.includes('edit')) {
    localStorage.removeItem(key);
    console.log(`Removed: ${key}`);
  }
});

console.log('✅ Test data cleared! Please refresh the page.');