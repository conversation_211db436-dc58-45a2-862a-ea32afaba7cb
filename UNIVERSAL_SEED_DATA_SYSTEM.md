# Universal Seed Data System

## Overview

The Universal Seed Data System provides a comprehensive foundation of menu items, recipes, and stock items for new restaurant setups. This system ensures that restaurants can start operating immediately with a professional menu structure that can be customized to their specific needs.

## Features

### 🍕 **Menu Categories**
- **Pizza**: Margherita, Pepperoni, Supreme, Meat Lovers (Medium & Large sizes)
- **Sandwiches**: Grilled Chicken, Club, Cheese (Regular size)
- **Tacos**: Beef, Chicken, Veggie (Single & 3-Pack)
- **Drinks**: Cola, Orange Juice, Coffee, Water (Small, Medium, Large)

### 📦 **Stock Items** (35+ items)
- **Flour & Grains**: Wheat flour, semolina flour
- **Dairy Products**: Mozzarella, cheddar, butter, milk
- **Vegetables**: Tomatoes, onions, lettuce, bell peppers, mushrooms
- **Meat & Protein**: Chicken breast, ground beef, pepperoni, ham
- **Seasonings**: Salt, pepper, oregano, basil, garlic powder
- **Oils & Liquids**: Olive oil, vegetable oil, water, yeast
- **Bread & Wraps**: Sandwich bread, flour tortillas
- **Beverages**: Cola syrup, orange juice, coffee beans
- **Packaging**: Pizza boxes, takeaway containers, cups, bags

### 🧪 **Sub-Recipes** (4 preparations)
- **Pizza Dough**: Complete recipe with flour, water, oil, salt, yeast
- **Pizza Sauce**: Tomato-based sauce with herbs and spices
- **Seasoned Ground Beef**: Prepared beef with onions and seasonings
- **Grilled Chicken Strips**: Seasoned and cooked chicken breast

### 🍳 **Menu Item Recipes** (40+ recipes)
Complete recipes for every menu item showing:
- Required ingredients (stock items and sub-recipes)
- Exact quantities needed
- Cost calculations
- Size variations

## File Structure

```
lib/db/v4/seed-data/
├── universal-seed-data.ts      # Core seed data definitions
├── seed-data-service.ts        # Service for managing seed data
└── README.md                   # This documentation

components/settings/
└── UniversalSeedDataSettings.tsx  # Owner-only settings interface

scripts/
└── test-seed-data.js          # Test script for validation
```

## Usage

### Manual Initialization Only

**Important:** Seed data is NEVER automatically initialized to prevent sync conflicts across devices. It must be manually initiated by the restaurant owner through the settings interface.

### Manual Management

```typescript
import { 
  initializeUniversalSeedData, 
  checkSeedDataExists, 
  clearSeedData 
} from '@/lib/db/v4';

// Check what seed data exists
const status = await checkSeedDataExists();

// Initialize seed data
const result = await initializeUniversalSeedData({
  skipIfExists: true,    // Don't overwrite existing data
  overwrite: false,      // Force overwrite if needed
  categories: ['pizza']  // Only specific categories
});

// Clear all seed data
await clearSeedData();
```

### Settings Interface (Owner Only)

Use the `UniversalSeedDataSettings` component in restaurant settings:

```tsx
import { UniversalSeedDataSettings } from '@/components/settings/UniversalSeedDataSettings';

function RestaurantSettings() {
  return (
    <div>
      <h1>Restaurant Settings</h1>
      <UniversalSeedDataSettings />
    </div>
  );
}
```

## Data Structure

### Stock Items
```typescript
interface StockItem {
  id: string;           // Unique identifier
  name: string;         // Display name
  category: string;     // Grouping category
  unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  minLevel?: number;    // Minimum stock level
  costPerUnit?: number; // Cost per unit
}
```

### Sub-Recipes
```typescript
interface SubRecipe {
  _id: string;
  type: 'sub-recipe';
  name: string;
  ingredients: RecipeIngredient[];
  yield: {
    quantity: number;
    unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  };
  costPerUnit?: number;
}
```

### Menu Items
```typescript
interface MenuItem {
  id: string;
  name: string;
  description?: string;
  prices: { [size: string]: number };
}
```

### Menu Item Recipes
```typescript
interface MenuItemRecipe {
  _id: string;
  type: 'menu-item-recipe';
  menuItemId: string;
  menuItemName?: string;
  size?: string;
  ingredients: (RecipeIngredient | SubRecipeIngredient)[];
  costPerUnit?: number;
}
```

## Cost Calculations

All recipes include realistic cost calculations based on:
- Ingredient costs per unit
- Portion sizes for different menu item sizes
- Sub-recipe costs factored into final items
- Packaging costs by order type (dine-in, takeaway, delivery)

### Example: Margherita Pizza (Medium)
- Pizza Dough (0.25 kg): $0.71
- Pizza Sauce (0.08 kg): $0.36
- Mozzarella (0.15 kg): $1.28
- Basil (0.002 kg): $0.0002
- **Total Cost**: $4.85
- **Selling Price**: $12.99
- **Profit Margin**: 62.6%

## Customization

### Adding New Categories
1. Add category to `UNIVERSAL_MENU_DOCUMENT.categories`
2. Create menu items with sizes and prices
3. Add corresponding recipes to `UNIVERSAL_MENU_ITEM_RECIPES`
4. Include required stock items and sub-recipes

### Modifying Existing Items
1. Update prices in menu items
2. Adjust recipe quantities and costs
3. Add or remove ingredients
4. Update packaging requirements

### Regional Adaptations
The seed data can be easily adapted for different regions:
- Change currency in cost calculations
- Modify portion sizes
- Add local ingredients
- Adjust menu categories for local preferences

## Testing

Run the test script to validate seed data integrity:

```bash
node scripts/test-seed-data.js
```

Tests verify:
- Data structure correctness
- Required fields presence
- Recipe-to-menu-item consistency
- Helper function functionality
- Cost calculation accuracy

## Best Practices

### For Restaurant Owners
1. **Manual Initialization Only**: Never initialize seed data automatically - always use the settings interface with proper confirmations
2. **Sync Awareness**: Ensure all devices are synchronized before making changes to prevent conflicts
3. **Review and Customize**: The seed data provides a starting point - customize prices, portions, and recipes to match your restaurant
4. **Update Costs**: Regularly update ingredient costs to maintain accurate profit margins
5. **Add Local Items**: Include popular local dishes and ingredients
6. **Seasonal Adjustments**: Modify availability and prices based on seasonal ingredients

### For Developers
1. **Maintain Consistency**: Ensure all recipes have corresponding menu items
2. **Validate Data**: Run tests after making changes
3. **Document Changes**: Update this documentation when adding new features
4. **Cost Accuracy**: Keep cost calculations realistic and up-to-date

## Integration Points

The seed data integrates with:
- **Inventory Management**: Stock items become inventory items
- **Order Processing**: Menu items and recipes drive order fulfillment
- **Cost Analysis**: Recipe costs enable profit margin analysis
- **Kitchen Operations**: Sub-recipes guide food preparation
- **Purchasing**: Stock items inform supplier orders

## Future Enhancements

Planned improvements:
- **Multiple Cuisines**: Add Italian, Mexican, Asian cuisine templates
- **Dietary Options**: Include vegetarian, vegan, gluten-free variants
- **Seasonal Menus**: Template for seasonal menu rotations
- **Franchise Support**: Multi-location menu standardization
- **Nutritional Data**: Include calorie and nutritional information
- **Allergen Information**: Track and display allergen data

## Support

For questions or issues with the seed data system:
1. Check the test script output for validation errors
2. Review the admin interface for status information
3. Examine database logs for initialization issues
4. Refer to the main application documentation

---

*This seed data system provides a professional foundation for restaurant operations while maintaining flexibility for customization and growth.*