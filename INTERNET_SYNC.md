# Internet Sync Implementation

This document describes the secure internet sync implementation that allows PouchDB/CouchDB synchronization across the internet when local network sync is not available.

## Architecture Overview

```
Mobile Device ←→ Next.js Server (Proxy) ←→ Desktop CouchDB
```

### Key Components

1. **Next.js Server APIs** - Device registry and HTTP proxy
2. **Mobile Internet Discovery** - Fallback when local mDNS fails  
3. **Desktop Internet Registration** - Register with server for discovery
4. **Secure HTTP Proxy** - Relay sync requests between mobile and desktop

## Security Features (No Encryption)

- **Restaurant-based isolation** - Devices can only sync within same restaurant
- **Database-level validation** - Proxy validates database belongs to restaurant
- **JWT authentication** - Existing token system for API access
- **Device registration** - Only registered devices can participate
- **Rate limiting** - 30 requests/minute per restaurant for discovery
- **Compound indexes** - Unique constraints on restaurant + device combinations
- **TTL cleanup** - Automatic removal of stale device records after 30 days
- **IP tracking** - Monitor device locations for security

## API Endpoints

### Device Registration
- `POST /api/sync/register-device` - Register desktop/mobile devices
- `POST /api/sync/heartbeat` - Keep device registration alive
- `POST /api/sync/unregister-device` - Clean up device registration

### Discovery
- `GET /api/sync/discover-peers` - Find available peers for restaurant (rate limited)

### Proxy
- `ALL /api/sync/proxy/[deviceId]/[...path]` - Proxy requests to desktop CouchDB

### Database Management
- `POST /api/sync/setup-indexes` - Create optimized indexes for tenant isolation

## Usage

### 1. Server Setup (Already Implemented)

The Next.js server now includes all necessary API endpoints for internet sync.

### 2. Mobile Setup

```typescript
import { MobileP2PSync } from '@/lib/p2p/mobile-p2p-sync';
import { configureMobileInternetSync } from '@/lib/sync/internet-sync-setup';

// Initialize mobile P2P sync
const mobileSync = new MobileP2PSync(deviceId);
await mobileSync.initialize(pouchDbInstance);

// Configure internet sync as fallback
configureMobileInternetSync(mobileSync, {
  serverUrl: 'https://your-server.com',
  authToken: 'your-jwt-token'
});
```

### 3. Desktop Setup

```typescript
import { configureDesktopInternetSync } from '@/lib/sync/internet-sync-setup';

// Configure internet sync (after P2P initialization)
configureDesktopInternetSync({
  serverUrl: 'https://your-server.com',
  authToken: 'your-jwt-token'
});
```

## How It Works

### Local Network (Primary)
1. Desktop runs CouchDB server on local network
2. Mobile discovers desktop via mDNS
3. Direct HTTP sync between mobile PouchDB and desktop CouchDB

### Internet Fallback (Secondary)
1. Desktop registers with Next.js server (IP, port, device ID)
2. Mobile queries server to discover registered desktops
3. Mobile sync requests go through server proxy to desktop
4. Server relays HTTP requests to desktop CouchDB
5. Responses flow back through server to mobile

### Authentication Flow
1. Both devices authenticate with JWT tokens
2. Server validates restaurant membership
3. Only devices in same restaurant can sync
4. Proxy requests include proper CouchDB authentication

### Conflict Resolution
- Uses existing PouchDB/CouchDB conflict resolution
- No changes to sync logic or data structure
- Multi-master sync supported (mobile can sync with multiple desktops)

## Benefits

- **Minimal Code Changes** - Reuses existing sync architecture
- **Secure Access Control** - Restaurant-based isolation without encryption
- **Reliable Fallback** - Works when local network unavailable
- **Cross-Platform** - Supports all existing platforms
- **Scalable** - Server handles multiple restaurants and devices

## Configuration

Set these environment variables on your Next.js server:

```env
JWT_SECRET=your-jwt-secret
MONGODB_URI=your-mongodb-connection-string
```

## Initial Setup

1. **Create database indexes** (run once):
```bash
curl -X POST https://your-server.com/api/sync/setup-indexes
```

2. **Verify tenant isolation**:
   - Each restaurant gets separate device registry
   - Database names must match `resto-{restaurantId}` pattern
   - Cross-tenant access returns 403 Forbidden

## Testing

1. **Local Sync Test** - Verify existing local sync still works
2. **Internet Discovery Test** - Mobile should discover desktop via server
3. **Internet Sync Test** - Mobile should sync through server proxy
4. **Fallback Test** - Mobile should fallback to internet when local fails
5. **Security Test** - Devices in different restaurants should not see each other

## Monitoring

The system logs all sync activity:
- Device registrations and heartbeats
- Peer discovery events  
- Proxy request routing
- Sync status updates

Check the application logs and server logs for troubleshooting.