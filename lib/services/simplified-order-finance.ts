'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { createCashTransaction } from '@/lib/db/v4/operations/cash-ops';
import { addTransactionToSession } from '@/lib/db/v4/operations/cash-session-ops';
import { updateOrder } from '@/lib/db/v4/operations/order-ops';
import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
import { calculateOrderCOGS, createConsumptionLogsForOrder } from '@/lib/db/v4/operations/inventory-ops';
import { kitchenPrintService } from './kitchen-print-service';


export interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
  sessionActivated?: boolean;
  orderUpdated?: boolean;
}

export interface UseSimplifiedOrderFinanceReturn {
  processOrderPayment: (
    order: OrderDocument,
    paymentMethod: string,
    receivedAmount?: number
  ) => Promise<OrderPaymentResult>;
  isProcessing: boolean;
  lastResult: OrderPaymentResult | null;
}

/**
 * Simplified Order Finance Service
 * 
 * REFACTORED: Works with new session system
 * - Sessions always exist, become active with sales
 * - Transactions are automatically linked to current session
 * - Clean separation between delivery and drawer money
 * - FIXED: Now properly updates order status after payment
 */
export function useSimplifiedOrderFinance(): UseSimplifiedOrderFinanceReturn {
  const { user } = useAuth();
  const { isReady } = useUnifiedDB();
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<OrderPaymentResult | null>(null);

  const processOrderPayment = async (
    order: OrderDocument,
    paymentMethod: string,
    receivedAmount?: number
  ): Promise<OrderPaymentResult> => {
    if (!isReady || !user) {
      const result: OrderPaymentResult = {
        orderId: order._id,
        success: false,
        error: 'Service not ready or user not authenticated',
        registeredInCaisse: false,
        orderUpdated: false
      };
      setLastResult(result);
      return result;
    }

    setIsProcessing(true);

    // Use safe payment processing with rollback capability
    const { safePaymentProcessing, createOrderUpdateRollback, createCashTransactionRollback } = await import('@/lib/db/v4/utils/transaction-rollback');

    try {
      return await safePaymentProcessing(async (rollback) => {
        console.log(`[OrderFinance] Processing payment for order ${order._id}:`, {
          orderType: order.orderType,
          total: order.total,
          paymentMethod,
          receivedAmount
        });

        let registeredInCaisse = false;
        let sessionActivated = false;
        let orderUpdated = false;

        // 1. FIRST: Calculate COGS and create consumption logs
        let totalCogs = 0;
        let grossProfit = 0;

        try {
          console.log(`[OrderFinance] Calculating COGS for order ${order._id}...`);

          // Safe stock consumption to prevent double consumption
          // Consumption logs are handled by order operations - no duplicate creation needed

          // Calculate total COGS
          totalCogs = await calculateOrderCOGS(order._id);
          grossProfit = order.total - totalCogs;

          console.log(`[OrderFinance] COGS calculation complete:`, {
            orderTotal: order.total,
            totalCogs,
            grossProfit,
            profitMargin: ((grossProfit / order.total) * 100).toFixed(2) + '%'
          });
        } catch (cogsError) {
          console.warn('[OrderFinance] COGS calculation failed:', cogsError);
          totalCogs = 0;
          grossProfit = 0;
        }

        // 2. SECOND: Update the order status to completed and paid with financial data
        try {
          // Check if order is already completed to prevent double processing
          if (order.status === 'completed' && order.paymentStatus === 'paid') {
            console.log(`[OrderFinance] Order ${order._id} already completed and paid, skipping update`);
            orderUpdated = false;
          } else {
            // Add rollback for order update
            rollback.addRollback(createOrderUpdateRollback(order._id, order));

            const actualAmount = typeof receivedAmount === 'number' ? receivedAmount : order.total;
            const change = receivedAmount ? Math.max(0, receivedAmount - order.total) : 0;

            await updateOrder(order._id, {
              status: 'completed',
              paymentStatus: 'paid',
              paymentMethod: paymentMethod as any,
              paymentDetails: {
                amountPaid: order.total,
                amountDue: 0,
                receivedAmount: actualAmount,
                change,
                paidAt: new Date().toISOString()
              },
              // Add financial metrics
              totalCogs,
              grossProfit,
              profitMargin: order.total > 0 ? (grossProfit / order.total) * 100 : 0
            });

            console.log(`[OrderFinance] Order ${order._id} status updated to completed/paid with COGS data`);
            orderUpdated = true;
          }
        } catch (orderError) {
          console.error('[OrderFinance] Error updating order status:', orderError);
          throw new Error(`Failed to update order status: ${orderError instanceof Error ? orderError.message : 'Unknown error'}`);
        }

        // 3. THIRD: Register all orders in the cash drawer (including delivery orders when collected in ordering page)
        try {
          const orderTypeLabel = order.orderType === 'dine-in' ? 'Sur place' :
            order.orderType === 'takeaway' ? 'À emporter' :
              order.orderType === 'takeout' ? 'À emporter' :
                order.orderType === 'table' ? 'Table' :
                  order.orderType === 'delivery' ? 'Livraison' :
                    'Commande';

          const orderSummary = `${order.items.length} article${order.items.length > 1 ? 's' : ''}`;
          const actualAmount = typeof receivedAmount === 'number' ? receivedAmount : order.total;

          // Determine transaction category based on delivery type
          let transactionCategory: string;
          let description: string;

          if (order.orderType === 'delivery') {
            // Check if this is a prepaid freelance delivery (treated as regular payment)
            const isPrepaidFreelance = order.deliveryPerson?.type === 'freelance' &&
              order.deliveryPerson?.paymentModel === 'prepaid';

            if (isPrepaidFreelance) {
              // Prepaid freelance deliveries are treated as regular order payments
              transactionCategory = 'order_payment';
              description = `${orderTypeLabel} (Prépayé): ${order.customer?.name || 'Client'} - ${orderSummary}`;
            } else {
              // Collection-based deliveries don't create transactions yet (handled by collection events)
              console.log(`[processOrderCompletion] Collection-based delivery: ${order._id} - transaction deferred to collection system`);

              // Still need to return proper result object
              const result: OrderPaymentResult = {
                orderId: order._id,
                success: true,
                registeredInCaisse: false, // Will be handled by collection system later
                sessionActivated: false,
                orderUpdated
              };
              setLastResult(result);
              return result;
            }
          } else {
            // Non-delivery orders are regular payments
            transactionCategory = 'order_payment';
            description = `${orderTypeLabel}: ${order.customer?.name || 'Client'} - ${orderSummary}`;
          }

          // Create the cash transaction
          const transaction = await createCashTransaction({
            type: 'sales',
            amount: order.total,
            description,
            time: new Date().toISOString(),
            performedBy: user?.name || 'System',
            relatedDocId: order._id,
            metadata: {
              transactionCategory,
              orderType: order.orderType,
              orderSummary: {
                id: order._id,
                total: order.total,
                items: orderSummary,
                orderType: order.orderType,
                customer: order.customer?.name || 'Client',
                tableId: order.tableId,
                createdAt: order.createdAt || new Date().toISOString()
              },
              paymentMethod,
              receivedAmount,
              change: receivedAmount ? Math.max(0, receivedAmount - order.total) : 0,
              itemCount: order.items.length,
              customer: order.customer?.name || 'Client',
              tableId: order.tableId,
              // For delivery orders, add delivery-specific metadata
              ...(order.orderType === 'delivery' && {
                deliveryPerson: order.deliveryPerson,
                customerAddress: order.customer?.address,
                // Only mark as collectedInOrderingPage for collection-based deliveries
                ...(transactionCategory === 'delivery_collection' && {
                  collectedInOrderingPage: true // Flag to indicate this was collected in ordering page, not via delivery collection system
                }),
                // Mark prepaid deliveries differently
                ...(transactionCategory === 'order_payment' && {
                  isPrepaidDelivery: true // Flag to indicate this is a prepaid freelance delivery
                })
              })
            }
          });

          // Add rollback for cash transaction
          rollback.addRollback(createCashTransactionRollback(transaction._id));

          console.log(`[OrderFinance] Cash transaction created for ${order.orderType} order:`, transaction._id);

          // Link transaction to current session (session always exists now)
          try {
            await addTransactionToSession(transaction._id);
            console.log(`[OrderFinance] Transaction linked to session`);
            sessionActivated = true; // This sale might activate the session
          } catch (sessionError) {
            console.warn('[OrderFinance] Session linking failed (non-critical):', sessionError);
          }

          registeredInCaisse = true;
        } catch (caisseError) {
          console.error('[OrderFinance] CRITICAL: Cash transaction creation failed:', caisseError);

          // If cash transaction fails, this is a critical error - throw to trigger rollback
          throw new Error(`Payment processing failed: ${caisseError instanceof Error ? caisseError.message : 'Cash transaction creation failed'}`);
        }

        // If we get here, everything succeeded

        // 🖨️ AUTOMATIC RECEIPT PRINTING
        try {
          console.log(`🖨️ [OrderFinance] Printing receipt for order ${order._id}`);

          const receiptResult = await kitchenPrintService.printReceipt(
            order,
            {
              method: paymentMethod,
              received: receivedAmount || order.total,
              change: Math.max(0, (receivedAmount || order.total) - order.total)
            },
            { fontSize: 'medium' }
          );

          if (receiptResult.success && receiptResult.actuallyPrinted) {
            console.log(`✅ [OrderFinance] Receipt printed successfully for order ${order._id}`);
          } else {
            console.warn(`⚠️ [OrderFinance] Receipt printing failed for order ${order._id}:`, receiptResult.error);
          }

        } catch (receiptError) {
          console.error(`❌ [OrderFinance] Receipt printing error for order ${order._id}:`, receiptError);
          // Don't fail the payment if receipt printing fails
        }

        // Import toast dynamically for success notification
        const { toast } = await import('sonner');
        toast.success('Payment Processed Successfully', {
          description: `Order ${order._id} has been paid and completed`,
          duration: 3000,
        });

        const result: OrderPaymentResult = {
          orderId: order._id,
          success: true,
          registeredInCaisse,
          sessionActivated,
          orderUpdated
        };
        setLastResult(result);
        return result;
      }); // End of safePaymentProcessing
    } catch (error) {
      console.error('[OrderFinance] Error processing payment:', error);
      
      // Import toast dynamically to avoid unused import
      const { toast } = await import('sonner');
      
      // Provide user-friendly error notification
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error('Payment Processing Failed', {
        description: `Failed to process payment for order ${order._id}: ${errorMessage}`,
        duration: 5000,
      });
      
      const result: OrderPaymentResult = {
        orderId: order._id,
        success: false,
        error: errorMessage,
        registeredInCaisse: false,
        orderUpdated: false
      };
      setLastResult(result);
      return result;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    processOrderPayment,
    isProcessing,
    lastResult
  };
}
