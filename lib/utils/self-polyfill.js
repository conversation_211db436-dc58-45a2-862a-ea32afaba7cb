/**
 * Self Polyfill for Electron/Static Builds
 * 
 * Provides a polyfill for the 'self' global variable that's available in browsers
 * but not in Node.js environments during the build process.
 */

// Check if we're in a browser environment
if (typeof self !== 'undefined') {
  module.exports = self;
} else if (typeof window !== 'undefined') {
  module.exports = window;
} else if (typeof global !== 'undefined') {
  module.exports = global;
} else {
  // Fallback: create a minimal global object
  module.exports = {};
}
