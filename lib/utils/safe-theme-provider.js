/**
 * Safe Theme Provider for Electron
 * 
 * This is a replacement for next-themes that prevents the forEach errors
 * that occur in Electron environments. It provides the same API but with
 * safer implementations.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext({
  theme: 'light',
  setTheme: () => {},
  themes: ['light', 'dark'],
  systemTheme: 'light',
  resolvedTheme: 'light',
});

export function ThemeProvider({
  children,
  defaultTheme = 'light',
  storageKey = 'theme',
  themes = ['light', 'dark'],
  attribute = 'class',
  enableSystem = false,
  disableTransitionOnChange = false,
  enableColorScheme = true,
  ...props
}) {
  const [theme, setThemeState] = useState(defaultTheme);
  const [mounted, setMounted] = useState(false);

  // Safe theme application that won't cause forEach errors
  const applyTheme = (newTheme) => {
    try {
      if (typeof document === 'undefined') return;
      
      const root = document.documentElement;
      
      if (attribute === 'class') {
        // Safely remove old theme classes
        themes.forEach(t => {
          if (root.classList && typeof root.classList.remove === 'function') {
            root.classList.remove(t);
          }
        });
        
        // Add new theme class
        if (root.classList && typeof root.classList.add === 'function') {
          root.classList.add(newTheme);
        }
      } else {
        // Set as attribute
        root.setAttribute(attribute, newTheme);
      }

      // Set color scheme if enabled
      if (enableColorScheme && root.style) {
        root.style.colorScheme = newTheme;
      }
    } catch (error) {
      console.warn('Theme application failed:', error);
    }
  };

  const setTheme = (newTheme) => {
    try {
      setThemeState(newTheme);
      applyTheme(newTheme);
      
      // Save to localStorage if available
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(storageKey, newTheme);
      }
    } catch (error) {
      console.warn('Theme setting failed:', error);
    }
  };

  // Initialize theme on mount
  useEffect(() => {
    try {
      let savedTheme = defaultTheme;
      
      // Try to get saved theme from localStorage
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(storageKey);
        if (stored && themes.includes(stored)) {
          savedTheme = stored;
        }
      }
      
      setThemeState(savedTheme);
      applyTheme(savedTheme);
      setMounted(true);
    } catch (error) {
      console.warn('Theme initialization failed:', error);
      setMounted(true);
    }
  }, []);

  const contextValue = {
    theme,
    setTheme,
    themes,
    systemTheme: 'light', // Simplified for Electron
    resolvedTheme: theme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Export default for compatibility
export default { ThemeProvider, useTheme };
