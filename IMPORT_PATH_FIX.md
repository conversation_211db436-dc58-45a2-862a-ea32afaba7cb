# Import Path Fix - Deprecated Function Error Resolution

## Issue
The deprecated `processOrderPayment` function in `lib/db/v4/operations/order-ops.ts` had an incorrect import path that caused a build error:

```
Module not found: Can't resolve '../../services/simplified-order-finance'
```

## Root Cause
The deprecated function was trying to dynamically import the `useSimplifiedOrderFinance` hook, but:
1. **Wrong Path**: The import path `../../services/simplified-order-finance` was incorrect
2. **Wrong Approach**: Trying to import a React hook in a non-React context
3. **Unnecessary Complexity**: The function should simply throw an error, not attempt delegation

## Solution
Simplified the deprecated function to:
1. **Remove problematic import** - No more dynamic import attempt
2. **Clear error message** - Descriptive error explaining the migration path
3. **Proper guidance** - Points developers to the correct replacement

## Fixed Code
```typescript
export async function processOrderPayment(
  orderId: string,
  paymentMethod: 'cash' | 'card' | 'online' | 'mixed',
  amountPaid: number,
  receivedAmount?: number
): Promise<OrderDocument> {
  console.warn('[processOrderPayment] DEPRECATED: Use useSimplifiedOrderFinance hook instead');
  
  // This function is deprecated and should not be used
  // Use the useSimplifiedOrderFinance hook in React components instead
  throw new Error(
    'processOrderPayment is deprecated. Use useSimplifiedOrderFinance hook from lib/services/simplified-order-finance instead. ' +
    'This provides better integration with cash sessions, COGS calculation, and transaction rollback capabilities.'
  );
}
```

## Verification
✅ **No Build Errors**: Import path issue resolved
✅ **No Breaking Changes**: Function still exported for compatibility
✅ **Clear Migration Path**: Error message guides developers to correct replacement
✅ **No Active Usage**: Confirmed no components are using the deprecated function

## Result
- **Build Error Fixed**: Application compiles successfully
- **Clean Deprecation**: Function provides clear guidance without causing import issues
- **Maintained Compatibility**: Export still exists but throws descriptive error
- **Developer Friendly**: Clear error message explains exactly what to use instead

The deprecated function now serves its intended purpose: preventing usage while guiding developers to the correct modern implementation.