// knowledge:start v4 fix - separate client and server components
import { Metadata, Viewport } from 'next';

// Font configuration with safe loading
let inter: any, almarai: any, tajawal: any, changa: any;

/**
 * Build targets:
 * - Static app (electron/static) for packaged builds
 * - Web server for landing (should use next/font/google for Arabic fonts)
 *
 * We treat landing route (/landing, and homepage if any) as web server content
 * by always enabling next/font on the web server. We detect web by absence of BUILD_TARGET.
 */
const isStaticBuild = process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static';
const isWebServer = !isStaticBuild;

// Safe font loading with try-catch
try {
  if (isWebServer) {
    // On web server (landing), use next/font for reliable loading & preloading
    const { Inter, Almarai, Tajawal, Changa } = require('next/font/google');

    inter = Inter({
      subsets: ['latin'],
      variable: '--font-inter',
      display: 'swap',
    });

    // Arabic fonts for comprehensive coverage on landing
    almarai = Almarai({
      subsets: ["arabic"],
      weight: ["300", "400", "700", "800"],
      display: "swap",
      variable: "--font-almarai",
    });

    tajawal = Tajawal({
      subsets: ["arabic"],
      weight: ["300", "400", "500", "700", "800", "900"],
      display: "swap",
      variable: "--font-tajawal",
    });

    changa = Changa({
      subsets: ["arabic"],
      weight: ["300", "400", "500", "600", "700", "800"],
      display: "swap",
      variable: "--font-changa",
    });
  } else {
    // Static packaged app: use system fallback; local @font-face comes from globals.css
    throw new Error('Static build - using fallback fonts + globals.css @font-face');
  }
} catch (error) {
  // Fallback for static builds or when Next.js fonts fail
  console.log('🔤 Using system fonts (static build or font loading failed)');
  inter = { variable: '--font-inter', className: '' };
  almarai = { variable: '--font-almarai', className: '' };
  tajawal = { variable: '--font-tajawal', className: '' };
  changa = { variable: '--font-changa', className: '' };
}

import './globals.css';
import { Providers } from './providers';
import { LayoutClientWrapper } from '@/components/layout-client-wrapper';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
// Server component doesn't directly import client components that use hooks

const stagewiseConfig = {
  plugins: []
};

export const metadata: Metadata = {
  title: 'Bistro | برنامج تسيير المطاعم في الجزائر - Logiciel de gestion de restaurant en Algérie',
  description: 'Bistro هو برنامج شامل لإدارة وتسيير المطاعم في الجزائر. حلول متكاملة للطلبات، المخزون، والموظفين. Logiciel complet pour la gestion de votre restaurant en Algérie.',
  keywords: ['برنامج تسيير مطاعم', 'ادارة مطاعم', 'الجزائر', 'logiciel de gestion restaurant', 'caisse restaurant', 'algérie', 'bistro', 'restaurant management'],
  applicationName: 'Bistro',
  authors: [{ name: 'Bistro' }],
  creator: 'Bistro',
  publisher: 'Bistro',
  alternates: {
    canonical: 'https://bistro.icu',
  },
  openGraph: {
    title: 'Bistro | أفضل برنامج لتسيير المطاعم في الجزائر',
    description: 'أدِر مطعمك بكفاءة مع Bistro. حلول متكاملة لإدارة الطلبات، المخزون، الموظفين، والمالية. مصمم خصيصاً للسوق الجزائري.',
    url: 'https://bistro.icu',
    siteName: 'Bistro',
    images: [
      {
        url: 'https://bistro.icu/og-image.png', // Replace with your actual OG image URL
        width: 1200,
        height: 630,
        alt: 'Bistro Restaurant Management Software',
      },
    ],
    locale: 'ar_DZ',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bistro | برنامج تسيير المطاعم في الجزائر',
    description: 'اكتشف Bistro، الحل الأمثل لإدارة مطعمك في الجزائر. سهل، فعال، ومصمم ليلبي كل احتياجاتك.',
    images: ['https://bistro.icu/twitter-image.png'], // Replace with your actual Twitter image URL
  },
};

export const viewport: Viewport = {
  themeColor: '#ffffff',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5, // Allow some zoom for accessibility
  userScalable: true, // Enable zoom for accessibility
  viewportFit: 'cover', // Handle safe areas on mobile devices
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning lang="en" dir="ltr" className="h-full overflow-x-hidden">
      <head>
        <link rel="alternate" href="https://bistro.icu" hrefLang="ar-DZ" />
        <link rel="apple-touch-icon" href={isStaticBuild ? "../icons/icon-192x192.png" : "/icons/icon-192x192.png"} />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />
        
        {/* Static builds use global @font-face from globals.css with absolute /fonts paths */}
        {/* Removed inline @font-face to avoid relative path resolution issues */}
        
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "Bistro",
              "operatingSystem": "Windows, macOS, Linux, Android, iOS",
              "applicationCategory": "BusinessApplication",
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "50"
              },
              "offers": {
                "@type": "Offer",
                "price": "25000",
                "priceCurrency": "DZD",
                "description": "عرض خاص للمستخدمين الأوائل بسعر سنوي مخفض."
              },
              "description": "برنامج Bistro هو الحل الأمثل لإدارة وتسيير المطاعم في الجزائر، يقدم أدوات شاملة للطلبات، المخزون، الموظفين، والتقارير المالية.",
              "url": "https://bistro.icu",
              "screenshot": "https://bistro.icu/screenshot.png"
            })
          }}
        />
      </head>
      <body className={`h-full text-foreground mobile-safe-area ${isWebServer ? 'font-tajawal' : ''} ${inter?.variable ?? ''} ${almarai?.variable ?? ''} ${tajawal?.variable ?? ''} ${changa?.variable ?? ''}`}>
        <Providers>
          <LayoutClientWrapper>
            {children}
          </LayoutClientWrapper>
        </Providers>
        {process.env.NODE_ENV === 'development' && <StagewiseToolbar config={stagewiseConfig} />}
      </body>
    </html>
  );
}
// knowledge:end
