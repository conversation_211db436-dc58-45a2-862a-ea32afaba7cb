# CRUSH.md

## Essential Commands

### Development
- `npm run dev` - Start development server (runs server.js)
- `npm run start` - Start production server

### Building
- `npm run build` - Build for web deployment
- `npm run build:static` - Build static export for offline use
- `npm run build:electron` - Build for Electron desktop app
- `npm run build:mobile` - Build for Capacitor mobile apps
- `npm run build:secure` - Secure build without sourcemaps

### Code Quality
- `npm run lint` - Run ESLint (configured to ignore errors during builds)
- TypeScript checking is handled automatically by Next.js (configured to ignore build errors)

### Testing
- No formal test framework is configured
- `npm run test:db-flow` - Test database connectivity
- Manual testing via debug pages at `/debug/*`

### Platform-Specific Development
- `npm run electron:dev` - Develop Electron app
- `npm run cap:dev:android` - Develop Android app
- `npm run cap:dev:ios` - Develop iOS app

## Code Style Guidelines

### Imports
- Use absolute imports with `@/*` path mapping
- Group imports in order: built-in modules, external packages, internal modules
- Use named imports when possible

### Formatting
- Use Tailwind CSS for styling with shadcn/ui components
- Follow Vercel Outline Convention (outline styling over card-like designs)
- Mobile-first responsive design approach
- Font sizes should be reasonable, avoiding overly small text

### TypeScript
- TypeScript with relaxed type checking (strict: false)
- Use Zod for schema validation with React Hook Form
- Components use React Hook Form with Zod validation

### Naming Conventions
- Use camelCase for variables and functions
- Use PascalCase for components and types
- Use UPPER_SNAKE_CASE for constants

### Error Handling
- Use Error Boundaries for catching UI errors
- Implement offline handling throughout the app
- Use context providers for shared state management

### Database Operations
- Database operations via custom hooks (useOrderV4, useStaffV4, etc.)
- Offline-first design with PouchDB for client-side database
- MongoDB for server database with automatic sync

## Important Notes
- No comments in code unless explicitly requested
- Source maps are disabled in production for security
- React Strict Mode is disabled to reduce hydration issues
- Build system handles Node.js module exclusion for static builds