#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔤 Testing font configuration for static builds...');

// Check layout.tsx for proper font handling
const layoutPath = path.join(__dirname, '..', 'app', 'layout.tsx');
const layoutContent = fs.readFileSync(layoutPath, 'utf8');

const fontChecks = [
  {
    name: 'Conditional font loading',
    test: layoutContent.includes('isStaticBuild') && layoutContent.includes('BUILD_TARGET'),
    fix: 'Add conditional font loading based on BUILD_TARGET'
  },
  {
    name: 'Google Fonts CDN for static builds',
    test: layoutContent.includes('fonts.googleapis.com') && layoutContent.includes('isStaticBuild &&'),
    fix: 'Add Google Fonts CDN links for static builds'
  },
  {
    name: 'System font fallbacks',
    test: layoutContent.includes("{ variable: '--font-inter', className: '' }"),
    fix: 'Provide system font fallbacks for static builds'
  }
];

let allPassed = true;

fontChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

// Check globals.css for font fallbacks
const cssPath = path.join(__dirname, '..', 'app', 'globals.css');
const cssContent = fs.readFileSync(cssPath, 'utf8');

const cssChecks = [
  {
    name: 'Font variable definitions',
    test: cssContent.includes('--font-inter:') && cssContent.includes('--font-tajawal:'),
    fix: 'Define CSS font variables with fallbacks'
  },
  {
    name: 'Font class definitions',
    test: cssContent.includes('.font-tajawal') && cssContent.includes('var(--font-tajawal)'),
    fix: 'Add font utility classes'
  }
];

cssChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

// Check Next.js config for font handling
const nextConfigPath = path.join(__dirname, '..', 'next.config.ts');
const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');

const configChecks = [
  {
    name: 'Font webpack rule for static builds',
    test: nextConfigContent.includes('\\.(woff|woff2|eot|ttf|otf)$/'),
    fix: 'Add webpack rule for font files in static builds'
  }
];

configChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

if (allPassed) {
  console.log('\n🎉 All font configuration checks passed!');
  console.log('💡 Fonts should now load properly in static Electron builds without CORS errors.');
  console.log('📝 Static builds will use Google Fonts CDN, non-static builds use Next.js fonts.');
} else {
  console.log('\n⚠️  Some font configuration checks failed. Please fix the issues above.');
  process.exit(1);
}

// Additional info
console.log('\n📋 Font Loading Strategy:');
console.log('  • Non-static builds: Next.js font optimization');
console.log('  • Static builds: Google Fonts CDN + CSS fallbacks');
console.log('  • Fallback: System fonts for offline scenarios');