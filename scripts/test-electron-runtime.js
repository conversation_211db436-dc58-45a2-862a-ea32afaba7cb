const fs = require('fs');
const path = require('path');

// Test script to check what paths are being generated in the HTML
const electronAppDir = '/Users/<USER>/Desktop/rest/shop/electron/app';

console.log('🔍 Testing HTML path generation...');

// Read index.html
const indexPath = path.join(electronAppDir, 'index.html');
if (fs.existsSync(indexPath)) {
  const content = fs.readFileSync(indexPath, 'utf8');
  
  console.log('\n📄 HTML Script Tags:');
  const scriptMatches = content.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
  scriptMatches.forEach((script, i) => {
    console.log(`${i + 1}: ${script}`);
  });
  
  console.log('\n📄 Inline Script Paths:');
  const inlineMatches = content.match(/"static\/chunks\/[^"]*"/g) || [];
  inlineMatches.forEach((match, i) => {
    console.log(`${i + 1}: ${match}`);
  });
  
  console.log('\n📄 Relative Paths:');
  const relativeMatches = content.match(/"\.\/_next\/static\/chunks\/[^"]*"/g) || [];
  relativeMatches.forEach((match, i) => {
    console.log(`${i + 1}: ${match}`);
  });
  
  console.log('\n📄 Absolute Paths:');
  const absoluteMatches = content.match(/"\/[^"]*"/g) || [];
  absoluteMatches.forEach((match, i) => {
    console.log(`${i + 1}: ${match}`);
  });
} else {
  console.log('❌ index.html not found');
}