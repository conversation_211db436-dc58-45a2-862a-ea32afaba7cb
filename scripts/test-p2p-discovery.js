#!/usr/bin/env node

/**
 * P2P Discovery Test Script
 * 
 * This script tests the P2P discovery system by:
 * 1. Scanning for the Next.js discovery endpoint (port 3000)
 * 2. Scanning for CouchDB servers (ports 5984-5987)
 * 3. Testing mDNS discovery if available
 * 
 * Run this script to debug P2P discovery issues.
 */

const http = require('http');
const { spawn } = require('child_process');

// Configuration
const DISCOVERY_PORTS = [3000]; // Next.js discovery endpoint
const COUCHDB_PORTS = [5984, 5985, 5986, 5987]; // CouchDB ports
const TEST_IPS = [
  '127.0.0.1',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '***********',
  '********',
  '********',
  '********'
];

console.log('🔍 P2P Discovery Test Script');
console.log('============================\n');

/**
 * Test HTTP endpoint
 */
async function testHttpEndpoint(ip, port, path = '/') {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const req = http.request({
      hostname: ip,
      port: port,
      path: path,
      method: 'GET',
      timeout: 2000
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const responseTime = Date.now() - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            success: true,
            ip,
            port,
            path,
            responseTime,
            data: jsonData,
            statusCode: res.statusCode
          });
        } catch (error) {
          resolve({
            success: true,
            ip,
            port,
            path,
            responseTime,
            data: data.substring(0, 200), // First 200 chars
            statusCode: res.statusCode,
            isJson: false
          });
        }
      });
    });
    
    req.on('error', () => {
      resolve({
        success: false,
        ip,
        port,
        path,
        responseTime: Date.now() - startTime
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        ip,
        port,
        path,
        responseTime: Date.now() - startTime,
        timeout: true
      });
    });
    
    req.end();
  });
}

/**
 * Test discovery endpoint
 */
async function testDiscoveryEndpoint(ip, port) {
  console.log(`  Testing discovery endpoint: http://${ip}:${port}/api/discovery/announce`);
  
  const result = await testHttpEndpoint(ip, port, '/api/discovery/announce');
  
  if (result.success) {
    console.log(`    ✅ SUCCESS (${result.responseTime}ms)`);
    
    if (result.data && typeof result.data === 'object') {
      console.log(`    📋 Device ID: ${result.data.deviceId || 'N/A'}`);
      console.log(`    🏠 Hostname: ${result.data.hostname || 'N/A'}`);
      console.log(`    🖥️  Platform: ${result.data.platform || 'N/A'}`);
      
      if (result.data.services && result.data.services.couchdb) {
        console.log(`    💾 CouchDB Port: ${result.data.services.couchdb.port}`);
        console.log(`    🔗 CouchDB Endpoint: ${result.data.services.couchdb.endpoint}`);
      }
    }
  } else {
    console.log(`    ❌ FAILED (${result.responseTime}ms)${result.timeout ? ' - TIMEOUT' : ''}`);
  }
  
  return result;
}

/**
 * Test CouchDB endpoint
 */
async function testCouchDBEndpoint(ip, port) {
  console.log(`  Testing CouchDB: http://${ip}:${port}/`);
  
  const result = await testHttpEndpoint(ip, port, '/');
  
  if (result.success) {
    console.log(`    ✅ SUCCESS (${result.responseTime}ms)`);
    
    if (result.data && typeof result.data === 'object') {
      console.log(`    💾 CouchDB: ${result.data.couchdb || 'N/A'}`);
      console.log(`    🆔 UUID: ${result.data.uuid || 'N/A'}`);
      console.log(`    📦 Version: ${result.data.version || 'N/A'}`);
      console.log(`    🏢 Vendor: ${result.data.vendor?.name || 'N/A'}`);
    }
  } else {
    console.log(`    ❌ FAILED (${result.responseTime}ms)${result.timeout ? ' - TIMEOUT' : ''}`);
  }
  
  return result;
}

/**
 * Test mDNS discovery using dns-sd command
 */
async function testMDNSDiscovery() {
  console.log('\n🔍 Testing mDNS Discovery');
  console.log('-------------------------');
  
  return new Promise((resolve) => {
    console.log('  Running: dns-sd -B _http._tcp');
    
    const dnssd = spawn('dns-sd', ['-B', '_http._tcp']);
    const timeout = setTimeout(() => {
      dnssd.kill();
      console.log('  ⏰ mDNS discovery timeout (10 seconds)');
      resolve([]);
    }, 10000);
    
    const services = [];
    
    dnssd.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`  📡 ${output.trim()}`);
      
      if (output.includes('Add') && output.includes('_http._tcp')) {
        services.push(output.trim());
      }
    });
    
    dnssd.stderr.on('data', (data) => {
      console.log(`  ⚠️  ${data.toString().trim()}`);
    });
    
    dnssd.on('close', (code) => {
      clearTimeout(timeout);
      console.log(`  🏁 dns-sd process exited with code ${code}`);
      resolve(services);
    });
    
    dnssd.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`  ❌ dns-sd error: ${error.message}`);
      resolve([]);
    });
  });
}

/**
 * Main test function
 */
async function runTests() {
  const results = {
    discoveryEndpoints: [],
    couchdbServers: [],
    mdnsServices: []
  };
  
  // Test discovery endpoints
  console.log('🔍 Testing Discovery Endpoints (Next.js)');
  console.log('=========================================');
  
  for (const ip of TEST_IPS) {
    for (const port of DISCOVERY_PORTS) {
      const result = await testDiscoveryEndpoint(ip, port);
      if (result.success) {
        results.discoveryEndpoints.push(result);
      }
    }
  }
  
  // Test CouchDB servers
  console.log('\n💾 Testing CouchDB Servers');
  console.log('===========================');
  
  for (const ip of TEST_IPS) {
    for (const port of COUCHDB_PORTS) {
      const result = await testCouchDBEndpoint(ip, port);
      if (result.success) {
        results.couchdbServers.push(result);
      }
    }
  }
  
  // Test mDNS discovery
  try {
    results.mdnsServices = await testMDNSDiscovery();
  } catch (error) {
    console.log(`\n❌ mDNS discovery failed: ${error.message}`);
  }
  
  // Summary
  console.log('\n📊 DISCOVERY SUMMARY');
  console.log('====================');
  console.log(`✅ Discovery Endpoints Found: ${results.discoveryEndpoints.length}`);
  console.log(`💾 CouchDB Servers Found: ${results.couchdbServers.length}`);
  console.log(`📡 mDNS Services Found: ${results.mdnsServices.length}`);
  
  if (results.discoveryEndpoints.length === 0 && results.couchdbServers.length === 0) {
    console.log('\n❌ NO SERVICES FOUND!');
    console.log('Troubleshooting tips:');
    console.log('1. Make sure the desktop app is running');
    console.log('2. Check that CouchDB is started (ports 5984-5987)');
    console.log('3. Verify Next.js dev server is running (port 3000)');
    console.log('4. Check firewall settings');
    console.log('5. Ensure devices are on the same network');
  } else {
    console.log('\n✅ DISCOVERY WORKING!');
    console.log('The mobile app should be able to find these services.');
  }
  
  return results;
}

// Run the tests
runTests().catch(console.error);
