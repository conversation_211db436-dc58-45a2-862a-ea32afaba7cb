#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing static auth page configuration...');

// Check if auth page exists and is properly configured
const authPagePath = path.join(__dirname, '..', 'app', 'auth', 'page.tsx');

if (!fs.existsSync(authPagePath)) {
  console.error('❌ Auth page not found');
  process.exit(1);
}

const authPageContent = fs.readFileSync(authPagePath, 'utf8');

// Check for static compatibility
const checks = [
  {
    name: 'No useSearchParams import',
    test: !authPageContent.includes("import { useSearchParams }"),
    fix: 'Remove useSearchParams import and use window.location.search instead'
  },
  {
    name: 'No Suspense wrapper',
    test: !authPageContent.includes('<Suspense'),
    fix: 'Remove Suspense wrapper from default export'
  },
  {
    name: 'Client-side URL handling',
    test: authPageContent.includes('window.location.search'),
    fix: 'Use window.location.search for URL parameters'
  },
  {
    name: 'Static auth endpoints',
    test: authPageContent.includes('https://bistro.icu/api/auth'),
    fix: 'Use bistro.icu server endpoints for authentication'
  }
];

let allPassed = true;

checks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

// Check Next.js config
const nextConfigPath = path.join(__dirname, '..', 'next.config.ts');
const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');

const configChecks = [
  {
    name: 'Conditional static export',
    test: nextConfigContent.includes("process.env.BUILD_TARGET === 'electron'"),
    fix: 'Make static export conditional on BUILD_TARGET'
  },
  {
    name: 'SPA fallback configured',
    test: nextConfigContent.includes('trailingSlash: true'),
    fix: 'Enable trailingSlash for SPA routing'
  }
];

configChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

// Check Electron config
const electronMainPath = path.join(__dirname, '..', 'electron', 'src', 'index.ts');
if (fs.existsSync(electronMainPath)) {
  const electronContent = fs.readFileSync(electronMainPath, 'utf8');
  
  const electronChecks = [
    {
      name: 'Electron-serve fallback',
      test: electronContent.includes("fallback: 'index.html'"),
      fix: 'Add fallback: "index.html" to electron-serve config'
    }
  ];

  electronChecks.forEach(check => {
    if (check.test) {
      console.log(`✅ ${check.name}`);
    } else {
      console.log(`❌ ${check.name} - ${check.fix}`);
      allPassed = false;
    }
  });
}

// Check font configuration for static builds
const layoutPath = path.join(__dirname, '..', 'app', 'layout.tsx');
if (fs.existsSync(layoutPath)) {
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  
  const fontChecks = [
    {
      name: 'Font CORS fix (CDN for static)',
      test: layoutContent.includes('fonts.googleapis.com') && layoutContent.includes('isStaticBuild'),
      fix: 'Use Google Fonts CDN for static builds to avoid CORS errors'
    },
    {
      name: 'Conditional font loading',
      test: layoutContent.includes('BUILD_TARGET === \'electron\'') && layoutContent.includes('BUILD_TARGET === \'static\''),
      fix: 'Make font loading conditional based on build target'
    }
  ];

  fontChecks.forEach(check => {
    if (check.test) {
      console.log(`✅ ${check.name}`);
    } else {
      console.log(`❌ ${check.name} - ${check.fix}`);
      allPassed = false;
    }
  });
}

if (allPassed) {
  console.log('\n🎉 All static auth configuration checks passed!');
  console.log('💡 The auth page should now work properly in static Electron builds.');
} else {
  console.log('\n⚠️  Some checks failed. Please fix the issues above.');
  process.exit(1);
}