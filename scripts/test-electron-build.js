#!/usr/bin/env node

/**
 * Comprehensive Electron Build Test Script
 * 
 * This script validates that the Next.js static export is properly configured
 * for Electron and tests for common issues that cause startup failures.
 */

const fs = require('fs');
const path = require('path');

const ELECTRON_APP_DIR = path.join(__dirname, '..', 'electron', 'app');
const REQUIRED_FILES = [
  'index.html',
  '_next/static/chunks',
  '_next/static/css'
];

function testElectronBuild() {
  console.log('🧪 Testing Electron Build Configuration...\n');
  
  let allTestsPassed = true;
  const issues = [];

  // Test 1: Check if electron app directory exists
  console.log('1️⃣ Checking Electron app directory...');
  if (!fs.existsSync(ELECTRON_APP_DIR)) {
    issues.push('❌ Electron app directory not found. Run `npm run build:electron` first.');
    allTestsPassed = false;
  } else {
    console.log('✅ Electron app directory exists');
  }

  // Test 2: Check required files
  console.log('\n2️⃣ Checking required files...');
  for (const file of REQUIRED_FILES) {
    const filePath = path.join(ELECTRON_APP_DIR, file);
    if (!fs.existsSync(filePath)) {
      issues.push(`❌ Missing required file/directory: ${file}`);
      allTestsPassed = false;
    } else {
      console.log(`✅ Found: ${file}`);
    }
  }

  // Test 3: Check index.html for problematic scripts
  console.log('\n3️⃣ Checking index.html for issues...');
  const indexPath = path.join(ELECTRON_APP_DIR, 'index.html');
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf8');
    
    // Check for absolute paths
    const absolutePaths = content.match(/(?:href|src)="\/_next\//g);
    if (absolutePaths) {
      issues.push(`❌ Found ${absolutePaths.length} absolute paths in index.html (should be relative)`);
      allTestsPassed = false;
    } else {
      console.log('✅ No absolute paths found');
    }

    // Check for problematic theme scripts
    const themeScripts = content.match(/<script>\(\(e,t,r,n,o,a,i,s\)=>/g);
    if (themeScripts) {
      issues.push(`❌ Found ${themeScripts.length} problematic theme scripts that may cause forEach errors`);
      allTestsPassed = false;
    } else {
      console.log('✅ No problematic theme scripts found');
    }

    // Check for proper relative paths
    const relativePaths = content.match(/(?:href|src)="\.\/_next\//g);
    if (relativePaths) {
      console.log(`✅ Found ${relativePaths.length} properly formatted relative paths`);
    }
  }

  // Test 4: Check webpack chunks for issues
  console.log('\n4️⃣ Checking webpack chunks...');
  const chunksDir = path.join(ELECTRON_APP_DIR, '_next', 'static', 'chunks');
  if (fs.existsSync(chunksDir)) {
    const chunks = fs.readdirSync(chunksDir).filter(f => f.endsWith('.js'));
    console.log(`✅ Found ${chunks.length} webpack chunks`);
    
    // Sample a few chunks to check for issues
    const sampleChunks = chunks.slice(0, 3);
    for (const chunk of sampleChunks) {
      const chunkPath = path.join(chunksDir, chunk);
      const chunkContent = fs.readFileSync(chunkPath, 'utf8');
      
      // Check for forEach usage that might cause issues
      if (chunkContent.includes('.forEach') && chunkContent.includes('Array.isArray')) {
        console.log(`⚠️  Chunk ${chunk} contains forEach usage - monitor for runtime errors`);
      }
    }
  }

  // Test 5: Check package.json scripts
  console.log('\n5️⃣ Checking build scripts...');
  const packagePath = path.join(__dirname, '..', 'package.json');
  if (fs.existsSync(packagePath)) {
    const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    if (pkg.scripts['build:electron']) {
      console.log('✅ build:electron script exists');
    } else {
      issues.push('❌ Missing build:electron script in package.json');
      allTestsPassed = false;
    }

    if (pkg.scripts['electron:static']) {
      console.log('✅ electron:static script exists');
    } else {
      issues.push('❌ Missing electron:static script in package.json');
      allTestsPassed = false;
    }
  }

  // Test 6: Check next.config.ts
  console.log('\n6️⃣ Checking Next.js configuration...');
  const nextConfigPath = path.join(__dirname, '..', 'next.config.ts');
  if (fs.existsSync(nextConfigPath)) {
    const configContent = fs.readFileSync(nextConfigPath, 'utf8');
    
    if (configContent.includes('output: \'export\'')) {
      console.log('✅ Static export configured');
    } else {
      issues.push('❌ Static export not configured in next.config.ts');
      allTestsPassed = false;
    }

    if (configContent.includes('publicPath: \'./_next/\'')) {
      console.log('✅ Relative public path configured');
    } else {
      issues.push('❌ Relative public path not configured');
      allTestsPassed = false;
    }
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Your Electron build should work correctly.');
    console.log('\n🚀 To test the Electron app:');
    console.log('   npm run electron:static');
  } else {
    console.log('❌ Some tests failed. Issues found:');
    issues.forEach(issue => console.log(`   ${issue}`));
    console.log('\n🔧 To fix issues:');
    console.log('   1. Run: npm run build:electron');
    console.log('   2. Check the build output for errors');
    console.log('   3. Run this test script again');
  }

  return allTestsPassed;
}

// Run the tests
if (require.main === module) {
  const success = testElectronBuild();
  process.exit(success ? 0 : 1);
}

module.exports = { testElectronBuild };
