#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing asset path configuration...');

// Check Next.js config
const nextConfigPath = path.join(__dirname, '..', 'next.config.ts');
const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');

const configChecks = [
  {
    name: 'Asset prefix for static builds',
    test: nextConfigContent.includes("assetPrefix: '../'") && nextConfigContent.includes("BUILD_TARGET === 'electron'"),
    fix: 'Set assetPrefix to "../" for static builds'
  },
  {
    name: 'Conditional static export',
    test: nextConfigContent.includes("output: 'export'") && nextConfigContent.includes("BUILD_TARGET === 'electron'"),
    fix: 'Enable static export for electron builds'
  }
];

let allPassed = true;

configChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

// Check layout for proper asset handling
const layoutPath = path.join(__dirname, '..', 'app', 'layout.tsx');
const layoutContent = fs.readFileSync(layoutPath, 'utf8');

const layoutChecks = [
  {
    name: 'Google Fonts CDN for static builds',
    test: layoutContent.includes('fonts.googleapis.com') && layoutContent.includes('isStaticBuild'),
    fix: 'Use Google Fonts CDN for static builds'
  },
  {
    name: 'Conditional icon paths',
    test: layoutContent.includes('../icons/') && layoutContent.includes('isStaticBuild'),
    fix: 'Use relative icon paths for static builds'
  }
];

layoutChecks.forEach(check => {
  if (check.test) {
    console.log(`✅ ${check.name}`);
  } else {
    console.log(`❌ ${check.name} - ${check.fix}`);
    allPassed = false;
  }
});

if (allPassed) {
  console.log('\n🎉 All asset path configuration checks passed!');
  console.log('💡 Asset paths should now work correctly in static Electron builds.');
  console.log('\n📋 Expected behavior:');
  console.log('  • Root page: ../_next/static/ → /_next/static/');
  console.log('  • Menu page: ../_next/static/ → /_next/static/');
  console.log('  • Auth page: ../_next/static/ → /_next/static/');
} else {
  console.log('\n⚠️  Some asset path checks failed. Please fix the issues above.');
  process.exit(1);
}

console.log('\n🚀 Ready to test with: BUILD_TARGET=electron npm run build');