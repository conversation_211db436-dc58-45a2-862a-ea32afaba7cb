#!/usr/bin/env node

/**
 * Test script for universal seed data
 * 
 * This script tests the seed data functionality to ensure it works correctly.
 * Run with: node scripts/test-seed-data.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Testing Universal Seed Data System...\n');

// Test data structure
const tests = [
  {
    name: 'Stock Items Structure',
    description: 'Verify stock items have required fields',
    test: () => {
      const { UNIVERSAL_STOCK_ITEMS } = require('../lib/db/v4/seed-data/universal-seed-data.ts');
      
      if (!Array.isArray(UNIVERSAL_STOCK_ITEMS)) {
        throw new Error('UNIVERSAL_STOCK_ITEMS should be an array');
      }
      
      if (UNIVERSAL_STOCK_ITEMS.length === 0) {
        throw new Error('UNIVERSAL_STOCK_ITEMS should not be empty');
      }
      
      const requiredFields = ['id', 'name', 'category', 'unit', 'costPerUnit'];
      const firstItem = UNIVERSAL_STOCK_ITEMS[0];
      
      for (const field of requiredFields) {
        if (!(field in firstItem)) {
          throw new Error(`Stock item missing required field: ${field}`);
        }
      }
      
      console.log(`✅ Found ${UNIVERSAL_STOCK_ITEMS.length} stock items with correct structure`);
    }
  },
  
  {
    name: 'Sub-Recipes Structure',
    description: 'Verify sub-recipes have required fields and ingredients',
    test: () => {
      const { UNIVERSAL_SUB_RECIPES } = require('../lib/db/v4/seed-data/universal-seed-data.ts');
      
      if (!Array.isArray(UNIVERSAL_SUB_RECIPES)) {
        throw new Error('UNIVERSAL_SUB_RECIPES should be an array');
      }
      
      if (UNIVERSAL_SUB_RECIPES.length === 0) {
        throw new Error('UNIVERSAL_SUB_RECIPES should not be empty');
      }
      
      const requiredFields = ['type', 'name', 'ingredients', 'yield'];
      const firstRecipe = UNIVERSAL_SUB_RECIPES[0];
      
      for (const field of requiredFields) {
        if (!(field in firstRecipe)) {
          throw new Error(`Sub-recipe missing required field: ${field}`);
        }
      }
      
      if (!Array.isArray(firstRecipe.ingredients)) {
        throw new Error('Sub-recipe ingredients should be an array');
      }
      
      if (firstRecipe.ingredients.length === 0) {
        throw new Error('Sub-recipe should have ingredients');
      }
      
      console.log(`✅ Found ${UNIVERSAL_SUB_RECIPES.length} sub-recipes with correct structure`);
    }
  },
  
  {
    name: 'Menu Document Structure',
    description: 'Verify menu document has categories and items',
    test: () => {
      const { UNIVERSAL_MENU_DOCUMENT } = require('../lib/db/v4/seed-data/universal-seed-data.ts');
      
      if (typeof UNIVERSAL_MENU_DOCUMENT !== 'object') {
        throw new Error('UNIVERSAL_MENU_DOCUMENT should be an object');
      }
      
      const requiredFields = ['_id', 'type', 'schemaVersion', 'categories'];
      for (const field of requiredFields) {
        if (!(field in UNIVERSAL_MENU_DOCUMENT)) {
          throw new Error(`Menu document missing required field: ${field}`);
        }
      }
      
      if (!Array.isArray(UNIVERSAL_MENU_DOCUMENT.categories)) {
        throw new Error('Menu categories should be an array');
      }
      
      if (UNIVERSAL_MENU_DOCUMENT.categories.length === 0) {
        throw new Error('Menu should have categories');
      }
      
      // Check first category structure
      const firstCategory = UNIVERSAL_MENU_DOCUMENT.categories[0];
      const requiredCategoryFields = ['id', 'name', 'items'];
      
      for (const field of requiredCategoryFields) {
        if (!(field in firstCategory)) {
          throw new Error(`Menu category missing required field: ${field}`);
        }
      }
      
      if (!Array.isArray(firstCategory.items)) {
        throw new Error('Category items should be an array');
      }
      
      console.log(`✅ Menu document has ${UNIVERSAL_MENU_DOCUMENT.categories.length} categories`);
      
      // Count total items
      const totalItems = UNIVERSAL_MENU_DOCUMENT.categories.reduce((sum, cat) => sum + cat.items.length, 0);
      console.log(`✅ Total menu items: ${totalItems}`);
    }
  },
  
  {
    name: 'Menu Item Recipes Structure',
    description: 'Verify menu item recipes match menu items',
    test: () => {
      const { 
        UNIVERSAL_MENU_ITEM_RECIPES, 
        UNIVERSAL_MENU_DOCUMENT 
      } = require('../lib/db/v4/seed-data/universal-seed-data.ts');
      
      if (!Array.isArray(UNIVERSAL_MENU_ITEM_RECIPES)) {
        throw new Error('UNIVERSAL_MENU_ITEM_RECIPES should be an array');
      }
      
      if (UNIVERSAL_MENU_ITEM_RECIPES.length === 0) {
        throw new Error('UNIVERSAL_MENU_ITEM_RECIPES should not be empty');
      }
      
      // Get all menu item IDs
      const menuItemIds = new Set();
      UNIVERSAL_MENU_DOCUMENT.categories.forEach(category => {
        category.items.forEach(item => {
          menuItemIds.add(item.id);
        });
      });
      
      // Check that all recipes have corresponding menu items
      const recipeItemIds = new Set();
      UNIVERSAL_MENU_ITEM_RECIPES.forEach(recipe => {
        if (!menuItemIds.has(recipe.menuItemId)) {
          throw new Error(`Recipe for unknown menu item: ${recipe.menuItemId}`);
        }
        recipeItemIds.add(recipe.menuItemId);
      });
      
      console.log(`✅ Found ${UNIVERSAL_MENU_ITEM_RECIPES.length} recipes for ${recipeItemIds.size} menu items`);
      
      // Check recipe structure
      const firstRecipe = UNIVERSAL_MENU_ITEM_RECIPES[0];
      const requiredFields = ['type', 'menuItemId', 'ingredients'];
      
      for (const field of requiredFields) {
        if (!(field in firstRecipe)) {
          throw new Error(`Menu item recipe missing required field: ${field}`);
        }
      }
      
      if (!Array.isArray(firstRecipe.ingredients)) {
        throw new Error('Recipe ingredients should be an array');
      }
    }
  },
  
  {
    name: 'Helper Functions',
    description: 'Test helper functions work correctly',
    test: () => {
      const { 
        generateSeedId, 
        addTimestamps, 
        prepareStockItems,
        prepareSubRecipes,
        prepareMenuDocument,
        prepareMenuItemRecipes
      } = require('../lib/db/v4/seed-data/universal-seed-data.ts');
      
      // Test generateSeedId
      const id1 = generateSeedId('test');
      const id2 = generateSeedId('test');
      
      if (id1 === id2) {
        throw new Error('generateSeedId should generate unique IDs');
      }
      
      if (!id1.startsWith('test-')) {
        throw new Error('generateSeedId should use provided prefix');
      }
      
      // Test addTimestamps
      const testObj = { name: 'test' };
      const withTimestamps = addTimestamps(testObj);
      
      if (!withTimestamps.createdAt || !withTimestamps.updatedAt) {
        throw new Error('addTimestamps should add createdAt and updatedAt');
      }
      
      // Test prepare functions
      const stockItems = prepareStockItems();
      const subRecipes = prepareSubRecipes();
      const menuDocument = prepareMenuDocument();
      const menuItemRecipes = prepareMenuItemRecipes();
      
      if (!Array.isArray(stockItems) || stockItems.length === 0) {
        throw new Error('prepareStockItems should return non-empty array');
      }
      
      if (!Array.isArray(subRecipes) || subRecipes.length === 0) {
        throw new Error('prepareSubRecipes should return non-empty array');
      }
      
      if (!menuDocument.createdAt || !menuDocument.updatedAt) {
        throw new Error('prepareMenuDocument should add timestamps');
      }
      
      if (!Array.isArray(menuItemRecipes) || menuItemRecipes.length === 0) {
        throw new Error('prepareMenuItemRecipes should return non-empty array');
      }
      
      console.log('✅ All helper functions work correctly');
    }
  }
];

// Run tests
let passed = 0;
let failed = 0;

for (const test of tests) {
  try {
    console.log(`🧪 ${test.name}: ${test.description}`);
    test.test();
    passed++;
    console.log('');
  } catch (error) {
    console.error(`❌ ${test.name} FAILED: ${error.message}\n`);
    failed++;
  }
}

// Summary
console.log('📊 Test Summary:');
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

if (failed > 0) {
  process.exit(1);
} else {
  console.log('\n🎉 All tests passed! Universal seed data system is working correctly.');
}