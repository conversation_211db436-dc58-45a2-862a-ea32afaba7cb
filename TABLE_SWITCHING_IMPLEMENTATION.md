# Table Switching & Multiple Orders Implementation

## Overview
Implemented a comprehensive table management system that allows:
1. **Table Switching**: Move orders between tables via a dedicated component
2. **Multiple Orders per Table**: Handle multiple orders on the same table with clear visual indicators

## Components Created

### 1. TableSwitchDialog (`components/orders/TableSwitchDialog.tsx`)
- **Purpose**: Dedicated component for switching orders between tables
- **Features**:
  - Minimalistic, compact design
  - Shows current table and order info
  - Displays all available tables with occupancy status
  - Shows number of existing orders per table
  - Prevents switching to the same table
  - Success feedback with toast notifications

### 2. TableSelectionHelper (`components/ordering/TableSelectionHelper.tsx`)
- **Purpose**: Visual helper for table selection in ordering interface
- **Features**:
  - Shows when an occupied table is selected
  - Displays table occupancy details (order count, time occupied)
  - Clear indication that a new order will be added to occupied table

## Modifications Made

### OrderList Component (`app/components/OrderList.tsx`)
- **Added**: Table switch button for dine-in orders
- **Added**: TableSwitchDialog integration
- **Condition**: Only shows for dine-in orders that are not cancelled or completed
- **Button Style**: Amber-colored to distinguish from other actions

### NewOrderingInterface Component (`app/components/NewOrderingInterface.tsx`)
- **Enhanced**: Table selection to show multiple orders per table
- **Added**: Order count badges in table selection dropdown
- **Added**: Visual indicators for occupied tables
- **Added**: TableSelectionHelper component integration
- **Enhanced**: Toast notifications for orders on occupied tables
- **Modified**: Table status calculation to include order count

## Key Features

### Table Switching
1. **Access**: Available in Orders List tab for dine-in orders
2. **UI**: Compact dialog with grid layout of tables
3. **Status Indicators**: 
   - Green badge: Free table
   - Clock badge: Occupied table with order count
   - Order IDs shown for occupied tables
4. **Feedback**: Success toast with table names

### Multiple Orders per Table
1. **Selection**: All tables selectable (no disabled occupied tables)
2. **Visual Cues**:
   - Badge showing number of existing orders
   - "Nouvelle commande sur table occupée" indicator
   - Amber warning box when occupied table selected
3. **Confirmation**: Special toast message for occupied table orders

## User Experience

### For Table Switching:
1. Go to Orders List tab
2. Find dine-in order to move
3. Click "Changer Table" button
4. Select new table from visual grid
5. Confirm with success feedback

### For Multiple Orders per Table:
1. In ordering interface, select dine-in order type
2. Choose table (occupied tables clearly marked)
3. Visual helper shows table status when occupied table selected
4. Create order with special confirmation message
5. Orders list shows multiple orders for same table

## Technical Implementation

### State Management
- Enhanced table status calculation to include `orderCount`
- Modified table options to allow occupied table selection
- Added table switch dialog state management

### Data Flow
- Table statuses calculated from active orders (pending, preparing, served)
- Order count tracked per table
- Real-time updates when orders are moved or created

### UI/UX Principles
- **Minimalistic**: Clean, compact design
- **Clear Indicators**: Visual cues for table occupancy
- **Feedback**: Toast notifications for all actions
- **Accessibility**: Proper labeling and keyboard navigation

## Benefits
1. **Flexibility**: Handle complex restaurant scenarios
2. **Clarity**: Clear visual indicators prevent confusion
3. **Efficiency**: Quick table switching without complex workflows
4. **Real-world Ready**: Handles common restaurant situations like shared tables