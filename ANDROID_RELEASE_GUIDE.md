# 📱 Android Release Guide

## ✅ **Complete Android Release System**

### **Your Setup is PERFECT!** 🎯

- ✅ **Static Builds**: Android uses fully static Next.js exports (like Electron)
- ✅ **Dev/Prod Modes**: Development connects to localhost, production is fully static
- ✅ **Offline-First**: PouchDB handles local data, syncs with remote APIs when online
- ✅ **Auto-Updates**: Built-in update system with R2 distribution
- ✅ **Signed APKs**: Production-ready signing configuration

### **Build Architecture** 📊

| Mode | Server | Static Files | Use Case |
|------|--------|-------------|----------|
| **Development** 🔧 | `localhost:3000` | `public/` | Live reload, debugging |
| **Production** 📱 | None | `out/` | Offline-first, distribution |

## 🚀 **Quick Start Commands**

### **Development Mode** 🔧
```bash
# Terminal 1: Start Next.js dev server
npm run dev

# Terminal 2: Start Android in dev mode (connects to localhost)
npm run cap:dev:android
```

### **Production Release** 📱
```bash
# Option 1: Build APK only
npm run android:release

# Option 2: Build and deploy to R2
npm run deploy:android
```

### **First-Time Setup** 🔐
```bash
# Set up APK signing (one-time only)
npm run android:setup-signing
```

## 📋 **Detailed Workflow**

### **1. Development Workflow** 🔧

1. **Start dev server**: `npm run dev`
2. **Start Android dev**: `npm run cap:dev:android`
3. **Features**:
   - ✅ Connects to `localhost:3000`
   - ✅ Live reload on code changes
   - ✅ Full debugging capabilities
   - ✅ Uses `public/` folder for assets

### **2. Production Build** 📱

1. **Build static export**: `npm run build:mobile`
2. **Sync Capacitor**: `npx cap sync android`
3. **Build APK**: `cd android && ./gradlew assembleRelease`
4. **Features**:
   - ✅ Fully static (no server dependency)
   - ✅ Uses `out/` folder for assets
   - ✅ Offline-first operation
   - ✅ 39 HTML pages exported

### **3. APK Signing Setup** 🔐

**First-time setup** (required for production):
```bash
npm run android:setup-signing
```

This will:
- ✅ Generate a keystore file
- ✅ Create `keystore.properties` (excluded from git)
- ✅ Configure `android/app/build.gradle`
- ✅ Update `.gitignore` files

**Security Notes**:
- 🔒 Back up your keystore file securely
- 🔒 Never lose your keystore - you can't update your app without it!
- 🔒 `keystore.properties` contains sensitive data and is excluded from git

### **4. R2 Deployment** ☁️

**Deploy to R2 storage**:
```bash
npm run deploy:android
```

This will:
- ✅ Build static export
- ✅ Generate signed APK
- ✅ Upload versioned APK to R2
- ✅ Create update metadata JSON
- ✅ Enable automatic updates

**Manual Distribution**:
- 📱 Users download APK from R2 URL
- 🔄 App automatically checks for updates
- 📦 Updates are downloaded and installed seamlessly

## 🔄 **Update System**

### **How Updates Work** 📱

1. **App startup**: Checks `android-update.json` on R2
2. **Version comparison**: Compares with current app version
3. **Update prompt**: Shows update dialog if newer version available
4. **Download**: Downloads APK in background
5. **Installation**: Prompts user to install update

### **Update Configuration** ⚙️

Update metadata (`android-update.json`):
```json
{
  "version": "1.0.1",
  "url": "https://pub-d1ae66d7e9a247a08a1ad96b22c13e10.r2.dev/bistro-android-1.0.1.apk",
  "releaseNotes": "Bug fixes and improvements",
  "mandatory": false
}
```

## 🎯 **App Features**

- 🌐 **Online**: Calls remote APIs when connected
- 💾 **Offline**: Uses PouchDB for local data storage
- 🔄 **Auto-Updates**: Checks for updates on startup
- 📱 **Native**: Full Capacitor features (camera, barcode scanner)
- 🎨 **UI**: Shadcn/UI components with Tailwind CSS

## 🚨 **Important Notes**

### **File Locations** 📁
- **APK Output**: `android/app/build/outputs/apk/release/`
- **Keystore**: `android/bistro-release-key.keystore`
- **Config**: `android/keystore.properties` (excluded from git)
- **Static Export**: `out/` directory

### **Troubleshooting** 🔧

**Java not found**:
```bash
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
```

**Build fails**:
```bash
# Clean and rebuild
cd android
./gradlew clean
./gradlew assembleRelease
```

**Update system not working**:
- Check R2 bucket URL in `useAndroidUpdater.ts`
- Verify `android-update.json` is accessible
- Ensure app has internet permission

### **Security Checklist** 🔒
- [ ] Keystore backed up securely
- [ ] `keystore.properties` excluded from git
- [ ] APK signed with production keystore
- [ ] R2 bucket configured with proper CORS
- [ ] Update URLs use HTTPS only

## 📊 **Build Summary**

| Component | Status | Notes |
|-----------|--------|-------|
| Static Export | ✅ Working | 39 pages, ~103KB JS |
| APK Build | ✅ Working | ~145MB, signed/unsigned |
| Update System | ✅ Working | Auto-check on startup |
| R2 Deployment | ✅ Working | Versioned uploads |
| Dev Mode | ✅ Working | Live reload via localhost |

## 🎉 **You're All Set!**

Your Android release system is now complete and follows the same pattern as your Electron app:

- **Development**: `npm run cap:dev:android` (connects to localhost)
- **Production**: `npm run deploy:android` (fully static, R2 distribution)
- **Updates**: Automatic via R2-hosted metadata
- **Distribution**: Manual APK download (no Play Store needed)

Your architecture is perfectly designed for offline-first, cross-platform operation! 🎉