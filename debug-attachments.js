// Debug script to check attachment storage
console.log('🔍 Debugging attachments...');

// Check if we're in Electron
const isElectron = typeof window !== 'undefined' && window.electronAPI;
console.log('Environment:', isElectron ? 'Electron' : 'Browser');

if (isElectron && window.electronAPI?.database) {
  const restaurantId = localStorage.getItem('currentRestaurantId') || 'resto-2b28a072-d490-40e6-b447-0b56e5610249';
  const dbIdentifier = `inventory-${restaurantId}`;
  console.log('Database identifier:', dbIdentifier);
  
  // Try to get a specific transaction
  const testTransactionId = 'purchase_tx_175478003300s_qwc530npt'; // From your error
  
  console.log('Testing transaction:', testTransactionId);
  
  // First check if document exists
  window.electronAPI.database.get(dbIdentifier, testTransactionId)
    .then(doc => {
      console.log('✅ Document exists:', doc._id);
      console.log('Has receipt image:', doc.hasReceiptImage);
      console.log('Receipt image filename:', doc.receiptImage);
      console.log('Document attachments:', doc._attachments);
      
      if (doc.receiptImage) {
        // Try to get the attachment
        console.log('🔍 Trying to get attachment:', doc.receiptImage);
        return window.electronAPI.database.getAttachment(dbIdentifier, testTransactionId, doc.receiptImage);
      }
    })
    .then(attachment => {
      if (attachment) {
        console.log('✅ Attachment retrieved:', attachment);
        console.log('Content type:', attachment.content_type);
        console.log('Data length:', attachment.data?.length);
      } else {
        console.log('❌ No attachment found');
      }
    })
    .catch(error => {
      console.error('❌ Error:', error);
    });
} else {
  console.log('❌ Not in Electron or electronAPI not available');
}